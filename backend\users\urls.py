from django.urls import path
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from . import views, admin_views

urlpatterns = [
    # Authentication endpoints
    path('login/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('register/', views.UserRegistrationView.as_view(), name='user-register'),
    path('profile/', views.UserProfileView.as_view(), name='user-profile'),

    # Admin dashboard endpoints
    path('admin/dashboard/stats/', admin_views.admin_dashboard_stats, name='admin-dashboard-stats'),
    path('admin/dashboard/recent-bookings/', admin_views.admin_recent_bookings, name='admin-recent-bookings'),
    path('admin/dashboard/booking-stats/', admin_views.admin_booking_stats, name='admin-booking-stats'),
]
