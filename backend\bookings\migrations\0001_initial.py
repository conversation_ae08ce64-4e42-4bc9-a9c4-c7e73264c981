# Generated by Django 4.2.24 on 2025-09-08 19:51

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('cars', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Booking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('customer_name', models.CharField(max_length=100)),
                ('customer_email', models.EmailField(max_length=254)),
                ('customer_phone', models.CharField(max_length=20)),
                ('driver_license_number', models.CharField(max_length=50)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('total_cost', models.DecimalField(decimal_places=2, max_digits=10)),
                ('status', models.CharField(choices=[('pending', 'Pending Review'), ('under_review', 'Under Review'), ('confirmed', 'Confirmed'), ('rejected', 'Rejected'), ('active', 'Active'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('special_requests', models.TextField(blank=True)),
                ('admin_notes', models.TextField(blank=True)),
                ('booking_date', models.DateTimeField(auto_now_add=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('car', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='cars.car')),
                ('pickup_location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pickup_bookings', to='cars.location')),
                ('return_location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='return_bookings', to='cars.location')),
            ],
            options={
                'db_table': 'bookings',
                'ordering': ['-created_at'],
            },
        ),
    ]
