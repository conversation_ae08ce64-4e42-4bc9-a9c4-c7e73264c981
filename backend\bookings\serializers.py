from rest_framework import serializers
from .models import Booking
from cars.serializers import CarListSerializer, LocationSerializer


class BookingSerializer(serializers.ModelSerializer):
    car_details = CarListSerializer(source='car', read_only=True)
    pickup_location_details = LocationSerializer(source='pickup_location', read_only=True)
    return_location_details = LocationSerializer(source='return_location', read_only=True)
    booking_reference = serializers.ReadOnlyField()
    rental_days = serializers.ReadOnlyField()
    
    class Meta:
        model = Booking
        fields = [
            'id', 'customer_name', 'customer_email', 'customer_phone',
            'driver_license_number', 'car', 'car_details', 'start_date', 'end_date',
            'pickup_location', 'pickup_location_details', 'return_location', 
            'return_location_details', 'total_cost', 'status', 'special_requests',
            'booking_reference', 'rental_days', 'booking_date', 'created_at'
        ]
        read_only_fields = ['booking_date', 'created_at']


class GuestBookingSerializer(serializers.ModelSerializer):
    """Serializer for guest bookings (no authentication required)"""
    booking_reference = serializers.ReadOnlyField()
    rental_days = serializers.ReadOnlyField()
    
    class Meta:
        model = Booking
        fields = [
            'customer_name', 'customer_email', 'customer_phone',
            'driver_license_number', 'car', 'start_date', 'end_date',
            'pickup_location', 'return_location', 'total_cost', 
            'special_requests', 'booking_reference', 'rental_days'
        ]
    
    def validate(self, data):
        """Validate booking dates and car availability"""
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        car = data.get('car')
        
        if start_date and end_date:
            if start_date >= end_date:
                raise serializers.ValidationError("End date must be after start date.")
        
        # Check car availability (basic check)
        if car and not car.is_available:
            raise serializers.ValidationError("Selected car is not available.")
        
        return data


class AdminBookingSerializer(serializers.ModelSerializer):
    """Extended serializer for admin with all fields"""
    car_details = CarListSerializer(source='car', read_only=True)
    pickup_location_details = LocationSerializer(source='pickup_location', read_only=True)
    return_location_details = LocationSerializer(source='return_location', read_only=True)
    booking_reference = serializers.ReadOnlyField()
    rental_days = serializers.ReadOnlyField()
    
    class Meta:
        model = Booking
        fields = [
            'id', 'customer_name', 'customer_email', 'customer_phone',
            'driver_license_number', 'user', 'car', 'car_details', 
            'start_date', 'end_date', 'pickup_location', 'pickup_location_details',
            'return_location', 'return_location_details', 'total_cost', 'status',
            'special_requests', 'admin_notes', 'booking_reference', 'rental_days',
            'booking_date', 'created_at', 'updated_at'
        ]
        read_only_fields = ['booking_date', 'created_at', 'updated_at']
