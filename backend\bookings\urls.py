from django.urls import path
from . import views

urlpatterns = [
    # Public booking endpoints
    path('guest/', views.GuestBookingCreateView.as_view(), name='guest-booking-create'),
    path('<int:pk>/', views.BookingDetailView.as_view(), name='booking-detail'),
    
    # User booking endpoints
    path('my-bookings/', views.UserBookingListView.as_view(), name='user-booking-list'),
    
    # Admin booking endpoints
    path('admin/', views.AdminBookingListView.as_view(), name='admin-booking-list'),
    path('admin/<int:pk>/', views.AdminBookingDetailView.as_view(), name='admin-booking-detail'),
    path('admin/<int:booking_id>/status/', views.update_booking_status, name='update-booking-status'),
]
