import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import Navbar from './components/Navbar';
import HomePage from './pages/HomePage';
import ContactPage from './pages/ContactPage';
import AdminLoginPage from './pages/AdminLoginPage';
import AdminDashboard from './pages/AdminDashboard';
import ProtectedRoute from './components/ProtectedRoute';

// Placeholder components for now
const CarListPage = () => <div className="p-8"><h1 className="text-2xl">Car List - Coming Soon</h1></div>;
const LoginPage = () => <div className="p-8"><h1 className="text-2xl">Login - Coming Soon</h1></div>;
const RegisterPage = () => <div className="p-8"><h1 className="text-2xl">Register - Coming Soon</h1></div>;

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* Public Routes */}
          <Route path="/" element={
            <div className="min-h-screen bg-gray-50">
              <Navbar />
              <main>
                <HomePage />
              </main>
            </div>
          } />
          <Route path="/cars" element={
            <div className="min-h-screen bg-gray-50">
              <Navbar />
              <main>
                <CarListPage />
              </main>
            </div>
          } />
          <Route path="/login" element={
            <div className="min-h-screen bg-gray-50">
              <Navbar />
              <main>
                <LoginPage />
              </main>
            </div>
          } />
          <Route path="/register" element={
            <div className="min-h-screen bg-gray-50">
              <Navbar />
              <main>
                <RegisterPage />
              </main>
            </div>
          } />
          <Route path="/contact" element={
            <div className="min-h-screen bg-gray-50">
              <Navbar />
              <main>
                <ContactPage />
              </main>
            </div>
          } />

          {/* Admin Routes */}
          <Route path="/admin" element={<AdminLoginPage />} />
          <Route path="/admin/dashboard" element={
            <ProtectedRoute requireAdmin={true}>
              <AdminDashboard />
            </ProtectedRoute>
          } />

          {/* 404 Route */}
          <Route path="*" element={
            <div className="min-h-screen bg-gray-50 flex justify-center items-center">
              <div className="text-center">
                <h1 className="text-4xl font-bold text-gray-900 mb-4">404</h1>
                <p className="text-gray-600">Page not found</p>
              </div>
            </div>
          } />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
