import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      const refreshToken = localStorage.getItem('refresh_token');
      if (refreshToken) {
        try {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh/`, {
            refresh: refreshToken,
          });

          const { access } = response.data;
          localStorage.setItem('access_token', access);

          return api(originalRequest);
        } catch (refreshError) {
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
          window.location.href = '/login';
        }
      }
    }

    return Promise.reject(error);
  }
);

// API functions
export const carAPI = {
  // Public car endpoints
  getCars: (params = {}) => api.get('/cars/', { params }),
  getCarById: (id) => api.get(`/cars/${id}/`),
  getLocations: () => api.get('/cars/locations/'),

  // Admin car endpoints
  adminGetCars: () => api.get('/cars/admin/cars/'),
  adminCreateCar: (data) => api.post('/cars/admin/cars/', data),
  adminUpdateCar: (id, data) => api.put(`/cars/admin/cars/${id}/`, data),
  adminDeleteCar: (id) => api.delete(`/cars/admin/cars/${id}/`),
};

export const bookingAPI = {
  // Public booking endpoints
  createGuestBooking: (data) => api.post('/bookings/guest/', data),
  getBookingById: (id) => api.get(`/bookings/${id}/`),

  // User booking endpoints
  getUserBookings: () => api.get('/bookings/my-bookings/'),

  // Admin booking endpoints
  adminGetBookings: (params = {}) => api.get('/bookings/admin/', { params }),
  adminUpdateBooking: (id, data) => api.put(`/bookings/admin/${id}/`, data),
  adminUpdateBookingStatus: (id, status, notes = '') =>
    api.put(`/bookings/admin/${id}/status/`, { status, admin_notes: notes }),
};

export const authAPI = {
  login: (credentials) => api.post('/auth/login/', credentials),
  register: (userData) => api.post('/auth/register/', userData),
  getProfile: () => api.get('/auth/profile/'),
  updateProfile: (data) => api.put('/auth/profile/', data),
};

export const adminAPI = {
  // Admin dashboard endpoints
  getDashboardStats: () => api.get('/auth/admin/dashboard/stats/'),
  getRecentBookings: () => api.get('/auth/admin/dashboard/recent-bookings/'),
  getBookingStats: () => api.get('/auth/admin/dashboard/booking-stats/'),
};

export default api;
