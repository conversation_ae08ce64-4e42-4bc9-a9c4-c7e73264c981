# Car Rental Website

A comprehensive car rental platform with customer-facing features and admin dashboard.

## Tech Stack

- **Frontend**: React 18 with React Router
- **Backend**: Django with Django REST Framework
- **Database**: SQLite (development)
- **Styling**: Tailwind CSS
- **Authentication**: JWT tokens

## Features

### Customer Features
- Browse available cars without registration
- Guest booking system (no login required)
- Optional user registration for booking history
- Car search and filtering
- Booking confirmation and reference tracking

### Admin Features
- Manage cars (CRUD operations)
- Review and approve/reject booking requests
- Customer management
- Dashboard analytics
- Booking status management

## Project Structure

```
car-rental/
├── backend/                 # Django project
│   ├── car_rental/         # Main Django app
│   ├── cars/               # Cars management app
│   ├── bookings/           # Booking system app
│   ├── users/              # User management app
│   └── requirements.txt
├── frontend/               # React application
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/          # Page components
│   │   ├── context/        # React contexts
│   │   ├── services/       # API services
│   │   └── utils/          # Utility functions
│   └── package.json
└── README.md
```

## Development Setup

### Backend Setup
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
python manage.py migrate
python manage.py createsuperuser
python manage.py runserver
```

### Frontend Setup
```bash
cd frontend
npm install
npm start
```

## API Endpoints

### Cars
- `GET /api/cars/` - List available cars
- `GET /api/cars/{id}/` - Get car details

### Bookings
- `POST /api/bookings/guest/` - Create guest booking
- `GET /api/bookings/{id}/` - Get booking details

### Admin
- `GET /api/admin/bookings/` - List all bookings
- `PUT /api/admin/bookings/{id}/status/` - Update booking status

## Development Timeline

- [x] Phase 1: Project setup and structure
- [ ] Phase 2: Database models and API
- [ ] Phase 3: Customer frontend
- [ ] Phase 4: Admin dashboard
- [ ] Phase 5: Testing and polish
