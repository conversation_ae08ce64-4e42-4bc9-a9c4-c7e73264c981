import React from 'react';

const StatCard = ({ title, value, icon, iconBgColor, iconColor }) => {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <div className={`${iconBgColor} p-3 rounded-lg`}>
            <div className={`w-6 h-6 ${iconColor}`}>
              {icon}
            </div>
          </div>
        </div>
        <div className="ml-4">
          <p className="text-sm font-medium text-gray-500">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
        </div>
      </div>
    </div>
  );
};

export default StatCard;
