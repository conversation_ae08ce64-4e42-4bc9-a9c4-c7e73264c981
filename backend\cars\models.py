from django.db import models


class Location(models.Model):
    """Pickup and return locations"""

    name = models.CharField(max_length=100)
    address = models.TextField()
    city = models.CharField(max_length=50)
    state = models.CharField(max_length=50)
    zip_code = models.CharField(max_length=10)
    phone = models.CharField(max_length=20)
    operating_hours = models.CharField(max_length=100, default="9:00 AM - 6:00 PM")
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.name} - {self.city}, {self.state}"

    class Meta:
        db_table = 'locations'


class Car(models.Model):
    """Car model for rental fleet"""

    CATEGORY_CHOICES = [
        ('economy', 'Economy'),
        ('compact', 'Compact'),
        ('mid-size', 'Mid-Size'),
        ('full-size', 'Full-Size'),
        ('luxury', 'Luxury'),
        ('suv', 'SUV'),
    ]

    FUEL_TYPE_CHOICES = [
        ('gasoline', 'Gasoline'),
        ('diesel', 'Diesel'),
        ('hybrid', 'Hybrid'),
        ('electric', 'Electric'),
    ]

    make = models.CharField(max_length=50)
    model = models.CharField(max_length=50)
    year = models.IntegerField()
    color = models.CharField(max_length=30)
    license_plate = models.CharField(max_length=20, unique=True)
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)
    daily_rate = models.DecimalField(max_digits=8, decimal_places=2)
    features = models.JSONField(default=list, blank=True)  # e.g., ["GPS", "Bluetooth", "AC"]
    image_url = models.URLField(blank=True)
    is_available = models.BooleanField(default=True)
    mileage = models.IntegerField(default=0)
    fuel_type = models.CharField(max_length=20, choices=FUEL_TYPE_CHOICES, default='gasoline')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.year} {self.make} {self.model} ({self.license_plate})"

    class Meta:
        db_table = 'cars'
        ordering = ['make', 'model', 'year']
