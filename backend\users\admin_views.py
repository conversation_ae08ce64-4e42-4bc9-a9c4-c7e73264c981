from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAdminUser
from django.db.models import Sum, Count, Q
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
from bookings.models import Booking
from cars.models import Car


@api_view(['GET'])
@permission_classes([IsAdminUser])
def admin_dashboard_stats(request):
    """Get dashboard statistics for admin"""
    
    # Calculate date ranges
    today = timezone.now().date()
    current_month = today.replace(day=1)
    last_month = (current_month - timedelta(days=1)).replace(day=1)
    current_year = today.replace(month=1, day=1)
    
    # Total earnings (sum of all confirmed/completed bookings)
    total_earnings = Booking.objects.filter(
        status__in=['confirmed', 'active', 'completed']
    ).aggregate(total=Sum('total_cost'))['total'] or Decimal('0')
    
    # Amounts paid (completed bookings)
    amounts_paid = Booking.objects.filter(
        status='completed'
    ).aggregate(total=Sum('total_cost'))['total'] or Decimal('0')
    
    # Total refunds (cancelled bookings that were confirmed)
    total_refunds = Booking.objects.filter(
        status='cancelled'
    ).aggregate(total=Sum('total_cost'))['total'] or Decimal('0')
    
    # Recent bookings count
    recent_bookings_count = Booking.objects.filter(
        created_at__gte=today - timedelta(days=7)
    ).count()
    
    # Active bookings count
    active_bookings_count = Booking.objects.filter(
        status='active'
    ).count()
    
    # Available cars count
    available_cars_count = Car.objects.filter(
        is_available=True
    ).count()
    
    # Total cars count
    total_cars_count = Car.objects.count()
    
    # Monthly earnings for chart (last 6 months)
    monthly_data = []
    for i in range(6):
        month_start = (current_month - timedelta(days=i*30)).replace(day=1)
        if i == 0:
            month_end = today
        else:
            next_month = month_start.replace(day=28) + timedelta(days=4)
            month_end = (next_month - timedelta(days=next_month.day)).replace(day=1) + timedelta(days=31)
            month_end = month_end.replace(day=1) - timedelta(days=1)
        
        earnings = Booking.objects.filter(
            status__in=['confirmed', 'active', 'completed'],
            created_at__date__gte=month_start,
            created_at__date__lte=month_end
        ).aggregate(total=Sum('total_cost'))['total'] or Decimal('0')
        
        paid = Booking.objects.filter(
            status='completed',
            created_at__date__gte=month_start,
            created_at__date__lte=month_end
        ).aggregate(total=Sum('total_cost'))['total'] or Decimal('0')
        
        refunds = Booking.objects.filter(
            status='cancelled',
            created_at__date__gte=month_start,
            created_at__date__lte=month_end
        ).aggregate(total=Sum('total_cost'))['total'] or Decimal('0')
        
        monthly_data.append({
            'month': month_start.strftime('%b'),
            'earnings': float(earnings),
            'amounts_paid': float(paid),
            'total_refunds': float(refunds)
        })
    
    # Reverse to get chronological order
    monthly_data.reverse()
    
    return Response({
        'total_earnings': float(total_earnings),
        'amounts_paid': float(amounts_paid),
        'total_refunds': float(total_refunds),
        'recent_bookings_count': recent_bookings_count,
        'active_bookings_count': active_bookings_count,
        'available_cars_count': available_cars_count,
        'total_cars_count': total_cars_count,
        'monthly_data': monthly_data
    })


@api_view(['GET'])
@permission_classes([IsAdminUser])
def admin_recent_bookings(request):
    """Get recent bookings for admin dashboard"""
    
    recent_bookings = Booking.objects.select_related('car').order_by('-created_at')[:10]
    
    bookings_data = []
    for booking in recent_bookings:
        bookings_data.append({
            'id': booking.id,
            'booking_reference': booking.booking_reference,
            'customer_name': booking.customer_name,
            'car_name': f"{booking.car.year} {booking.car.make} {booking.car.model}",
            'total_cost': float(booking.total_cost),
            'rental_days': booking.rental_days,
            'status': booking.status,
            'created_at': booking.created_at.isoformat()
        })
    
    return Response({
        'recent_bookings': bookings_data
    })


@api_view(['GET'])
@permission_classes([IsAdminUser])
def admin_booking_stats(request):
    """Get booking statistics by status"""
    
    status_stats = {}
    for status_code, status_name in Booking.STATUS_CHOICES:
        count = Booking.objects.filter(status=status_code).count()
        status_stats[status_code] = {
            'name': status_name,
            'count': count
        }
    
    return Response({
        'booking_stats': status_stats
    })
