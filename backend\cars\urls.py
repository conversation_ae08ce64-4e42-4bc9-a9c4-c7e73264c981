from django.urls import path
from . import views

urlpatterns = [
    # Public car endpoints
    path('', views.CarListView.as_view(), name='car-list'),
    path('<int:pk>/', views.CarDetailView.as_view(), name='car-detail'),
    
    # Public location endpoints
    path('locations/', views.LocationListView.as_view(), name='location-list'),
    
    # Admin car endpoints
    path('admin/cars/', views.AdminCarListCreateView.as_view(), name='admin-car-list'),
    path('admin/cars/<int:pk>/', views.AdminCarDetailView.as_view(), name='admin-car-detail'),
    
    # Admin location endpoints
    path('admin/locations/', views.AdminLocationListCreateView.as_view(), name='admin-location-list'),
    path('admin/locations/<int:pk>/', views.AdminLocationDetailView.as_view(), name='admin-location-detail'),
]
