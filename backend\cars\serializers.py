from rest_framework import serializers
from .models import Car, Location


class LocationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Location
        fields = ['id', 'name', 'address', 'city', 'state', 'zip_code', 'phone', 'operating_hours']


class CarSerializer(serializers.ModelSerializer):
    class Meta:
        model = Car
        fields = [
            'id', 'make', 'model', 'year', 'color', 'license_plate',
            'category', 'daily_rate', 'features', 'image_url', 
            'is_available', 'mileage', 'fuel_type', 'created_at'
        ]
        read_only_fields = ['created_at']


class CarListSerializer(serializers.ModelSerializer):
    """Simplified serializer for car listings"""
    class Meta:
        model = Car
        fields = [
            'id', 'make', 'model', 'year', 'category', 
            'daily_rate', 'image_url', 'fuel_type'
        ]
