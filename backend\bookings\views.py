from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated, IsAdminUser
from django_filters.rest_framework import DjangoF<PERSON>erBackend
from .models import Booking
from .serializers import BookingSerializer, GuestBookingSerializer, AdminBookingSerializer


class GuestBookingCreateView(generics.CreateAPIView):
    """Public API for guest bookings (no authentication required)"""
    queryset = Booking.objects.all()
    serializer_class = GuestBookingSerializer
    permission_classes = [AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        booking = serializer.save(status='pending')

        # Return booking details with reference number
        response_serializer = BookingSerializer(booking)
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)


class BookingDetailView(generics.RetrieveAPIView):
    """Public API to get booking details by ID"""
    queryset = Booking.objects.all()
    serializer_class = BookingSerializer
    permission_classes = [AllowAny]


class UserBookingListView(generics.ListAPIView):
    """API for authenticated users to view their bookings"""
    serializer_class = BookingSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Booking.objects.filter(user=self.request.user)


# Admin Views
class AdminBookingListView(generics.ListAPIView):
    """Admin API to list all bookings"""
    queryset = Booking.objects.all()
    serializer_class = AdminBookingSerializer
    permission_classes = [IsAdminUser]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['status', 'car__category', 'start_date', 'end_date']
    ordering = ['-created_at']


class AdminBookingDetailView(generics.RetrieveUpdateAPIView):
    """Admin API to view and update booking details"""
    queryset = Booking.objects.all()
    serializer_class = AdminBookingSerializer
    permission_classes = [IsAdminUser]


@api_view(['PUT'])
@permission_classes([IsAdminUser])
def update_booking_status(request, booking_id):
    """Admin API to update booking status"""
    try:
        booking = Booking.objects.get(id=booking_id)
    except Booking.DoesNotExist:
        return Response({'error': 'Booking not found'}, status=status.HTTP_404_NOT_FOUND)

    new_status = request.data.get('status')
    admin_notes = request.data.get('admin_notes', '')

    if new_status not in dict(Booking.STATUS_CHOICES):
        return Response({'error': 'Invalid status'}, status=status.HTTP_400_BAD_REQUEST)

    booking.status = new_status
    if admin_notes:
        booking.admin_notes = admin_notes
    booking.save()

    serializer = AdminBookingSerializer(booking)
    return Response(serializer.data)
