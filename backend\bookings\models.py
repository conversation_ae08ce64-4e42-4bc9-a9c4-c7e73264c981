from django.db import models
from django.contrib.auth import get_user_model
from cars.models import Car, Location

User = get_user_model()


class Booking(models.Model):
    """Booking model supporting both guest and registered user bookings"""

    STATUS_CHOICES = [
        ('pending', 'Pending Review'),
        ('under_review', 'Under Review'),
        ('confirmed', 'Confirmed'),
        ('rejected', 'Rejected'),
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]

    # Guest booking fields (required for all bookings)
    customer_name = models.CharField(max_length=100)
    customer_email = models.EmailField()
    customer_phone = models.CharField(max_length=20)
    driver_license_number = models.CharField(max_length=50)

    # Optional registered user link
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    # Booking details
    car = models.ForeignKey(Car, on_delete=models.CASCADE)
    start_date = models.DateField()
    end_date = models.DateField()
    pickup_location = models.ForeignKey(Location, on_delete=models.CASCADE, related_name='pickup_bookings')
    return_location = models.ForeignKey(Location, on_delete=models.CASCADE, related_name='return_bookings')

    # Pricing and status
    total_cost = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    special_requests = models.TextField(blank=True)
    admin_notes = models.TextField(blank=True)  # Internal notes for admin

    # Timestamps
    booking_date = models.DateTimeField(auto_now_add=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Booking #{self.id} - {self.customer_name} - {self.car}"

    @property
    def booking_reference(self):
        """Generate a booking reference number"""
        return f"CR{self.id:06d}"

    @property
    def rental_days(self):
        """Calculate number of rental days"""
        return (self.end_date - self.start_date).days + 1

    class Meta:
        db_table = 'bookings'
        ordering = ['-created_at']
