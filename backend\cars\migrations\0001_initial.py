# Generated by Django 4.2.24 on 2025-09-08 19:51

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Car',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('make', models.CharField(max_length=50)),
                ('model', models.CharField(max_length=50)),
                ('year', models.IntegerField()),
                ('color', models.CharField(max_length=30)),
                ('license_plate', models.CharField(max_length=20, unique=True)),
                ('category', models.CharField(choices=[('economy', 'Economy'), ('compact', 'Compact'), ('mid-size', 'Mid-Size'), ('full-size', 'Full-Size'), ('luxury', 'Luxury'), ('suv', 'SUV')], max_length=20)),
                ('daily_rate', models.DecimalField(decimal_places=2, max_digits=8)),
                ('features', models.JSONField(blank=True, default=list)),
                ('image_url', models.URLField(blank=True)),
                ('is_available', models.BooleanField(default=True)),
                ('mileage', models.IntegerField(default=0)),
                ('fuel_type', models.CharField(choices=[('gasoline', 'Gasoline'), ('diesel', 'Diesel'), ('hybrid', 'Hybrid'), ('electric', 'Electric')], default='gasoline', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'cars',
                'ordering': ['make', 'model', 'year'],
            },
        ),
        migrations.CreateModel(
            name='Location',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('address', models.TextField()),
                ('city', models.CharField(max_length=50)),
                ('state', models.CharField(max_length=50)),
                ('zip_code', models.CharField(max_length=10)),
                ('phone', models.CharField(max_length=20)),
                ('operating_hours', models.CharField(default='9:00 AM - 6:00 PM', max_length=100)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'db_table': 'locations',
            },
        ),
    ]
