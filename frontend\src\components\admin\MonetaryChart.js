import React from 'react';

const MonetaryChart = ({ monthlyData = [] }) => {
  // Use provided data or fallback to mock data
  const chartData = monthlyData.length > 0 ? monthlyData : [
    { month: 'Jan', earnings: 120, amounts_paid: 100, total_refunds: 80 },
    { month: 'Feb', earnings: 90, amounts_paid: 85, total_refunds: 70 },
    { month: 'Mar', earnings: 140, amounts_paid: 110, total_refunds: 90 },
    { month: 'Apr', earnings: 180, amounts_paid: 140, total_refunds: 120 },
    { month: 'May', earnings: 160, amounts_paid: 130, total_refunds: 110 },
    { month: 'Jun', earnings: 120, amounts_paid: 100, total_refunds: 85 }
  ];

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Monetary Statistics</h3>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <span className="text-sm text-gray-600">Earnings</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
            <span className="text-sm text-gray-600">Amounts Paid</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <span className="text-sm text-gray-600">Total Refunds</span>
          </div>
          <select className="text-sm border border-gray-300 rounded px-2 py-1">
            <option>Monthly</option>
            <option>Weekly</option>
            <option>Yearly</option>
          </select>
        </div>
      </div>

      {/* Simple SVG Chart */}
      <div className="relative h-64">
        <svg className="w-full h-full" viewBox="0 0 600 200">
          {/* Grid lines */}
          <defs>
            <pattern id="grid" width="100" height="40" patternUnits="userSpaceOnUse">
              <path d="M 100 0 L 0 0 0 40" fill="none" stroke="#f3f4f6" strokeWidth="1" />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />

          {/* Y-axis labels */}
          <text x="10" y="20" className="text-xs fill-gray-500">200k</text>
          <text x="10" y="60" className="text-xs fill-gray-500">150k</text>
          <text x="10" y="100" className="text-xs fill-gray-500">100k</text>
          <text x="10" y="140" className="text-xs fill-gray-500">50k</text>
          <text x="10" y="180" className="text-xs fill-gray-500">0k</text>

          {/* Chart lines */}
          {/* Earnings line (blue) */}
          <polyline
            fill="none"
            stroke="#3b82f6"
            strokeWidth="3"
            points="80,80 160,100 240,60 320,20 400,40 480,80"
          />

          {/* Amounts Paid line (purple) */}
          <polyline
            fill="none"
            stroke="#8b5cf6"
            strokeWidth="3"
            points="80,100 160,115 240,90 320,60 400,70 480,100"
          />

          {/* Total Refunds line (red) */}
          <polyline
            fill="none"
            stroke="#ef4444"
            strokeWidth="3"
            strokeDasharray="5,5"
            points="80,120 160,130 240,110 320,80 400,90 480,115"
          />

          {/* Data points */}
          {chartData.map((_, index) => (
            <g key={index}>
              <circle cx={80 + index * 80} cy={80 + index * 10} r="4" fill="#3b82f6" />
              <circle cx={80 + index * 80} cy={100 + index * 5} r="4" fill="#8b5cf6" />
              <circle cx={80 + index * 80} cy={120 + index * 3} r="4" fill="#ef4444" />
            </g>
          ))}

          {/* X-axis labels */}
          {chartData.map((data, index) => (
            <text key={index} x={80 + index * 80} y="195" className="text-xs fill-gray-500 text-anchor-middle">
              {data.month}
            </text>
          ))}
        </svg>
      </div>
    </div>
  );
};

export default MonetaryChart;
