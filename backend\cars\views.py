from rest_framework import generics, filters
from rest_framework.permissions import AllowAny, IsAuthenticated, IsAdminUser
from django_filters.rest_framework import DjangoFilterBackend
from .models import Car, Location
from .serializers import CarSerializer, CarListSerializer, LocationSerializer


class CarListView(generics.ListAPIView):
    """Public API to list available cars"""
    queryset = Car.objects.filter(is_available=True)
    serializer_class = CarListSerializer
    permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['category', 'fuel_type', 'year']
    search_fields = ['make', 'model']
    ordering_fields = ['daily_rate', 'year', 'make']
    ordering = ['daily_rate']


class CarDetailView(generics.RetrieveAPIView):
    """Public API to get car details"""
    queryset = Car.objects.filter(is_available=True)
    serializer_class = CarSerializer
    permission_classes = [AllowAny]


class LocationListView(generics.ListAPIView):
    """Public API to list active locations"""
    queryset = Location.objects.filter(is_active=True)
    serializer_class = LocationSerializer
    permission_classes = [AllowAny]


# Admin Views
class AdminCarListCreateView(generics.ListCreateAPIView):
    """Admin API to list and create cars"""
    queryset = Car.objects.all()
    serializer_class = CarSerializer
    permission_classes = [IsAdminUser]


class AdminCarDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Admin API to manage individual cars"""
    queryset = Car.objects.all()
    serializer_class = CarSerializer
    permission_classes = [IsAdminUser]


class AdminLocationListCreateView(generics.ListCreateAPIView):
    """Admin API to list and create locations"""
    queryset = Location.objects.all()
    serializer_class = LocationSerializer
    permission_classes = [IsAdminUser]


class AdminLocationDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Admin API to manage individual locations"""
    queryset = Location.objects.all()
    serializer_class = LocationSerializer
    permission_classes = [IsAdminUser]
